import { PresetLocationMap } from './presetType';
import { subwayPresets } from './core/subway';
import { LocationType, LocationSubtype } from '../../../shared/enums';

// Маппинг типов локаций к пресетам
export const PRESET_MAP: Record<LocationType, Record<string, PresetLocationMap[]>> = {
  [LocationType.UNDERGROUND]: {
    [LocationSubtype.SUBWAY]: subwayPresets,
    // Добавить другие underground пресеты когда будут готовы
  },
  [LocationType.INDOOR]: {
    // Добавить indoor пресеты когда будут готовы
    // [LocationSubtype.SCHOOL]: schoolPresets,
    // [LocationSubtype.POLICE]: policePresets,
  },
  [LocationType.OUTDOOR]: {
    // Заглушка для outdoor
  },
  [LocationType.BEACH]: {
    // Заглушка для beach
  }
};

// Функция для получения пресетов по типу локации и подтипу
export function getPresetsForLocation(locationType: LocationType, subtype: string): PresetLocationMap[] | null {
  const typePresets = PRESET_MAP[locationType];
  if (!typePresets) return null;

  return typePresets[subtype] || null;
}
