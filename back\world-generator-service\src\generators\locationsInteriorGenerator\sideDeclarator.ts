import { TransferLocation, PointWithNumber } from '../../shared/types/Location';
import { LocationDecorations } from '../../shared/enums';

/**
 * Editable equivalence groups: decorations listed in the same inner array are
 * considered equal for the purposes of side detection (they will connect).
 *
 * Add new groups here when you want more types to be treated as identical.
 * Example below treats wall/window/door as the same group.
 */
export const EQUIVALENCE_GROUPS: string[][] = [
  // treat these as the same for sides: wall, window, door
  [
    LocationDecorations.WALL,
    LocationDecorations.WINDOW,
    LocationDecorations.DOOR,
    LocationDecorations.FENCE,
    LocationDecorations.PARTITION,

  ]
];

/**
 * Programmatic helper to register equivalence groups at runtime (optional).
 */
export function addEquivalenceGroup(group: string[]) {
  if (!group || !group.length) return;
  EQUIVALENCE_GROUPS.push(group);
}

// Build a lookup map from decoration -> canonical group id
function buildEquivalenceLookup(): Map<string, string> {
  const map = new Map<string, string>();
  for (const group of EQUIVALENCE_GROUPS) {
    const id = group[0];
    for (const t of group) map.set(t, id);
  }
  return map;
}

function areEquivalent(a: string, b: string, lookup: Map<string, string>): boolean {
  if (a === b) return true;
  const ca = lookup.get(a) ?? a;
  const cb = lookup.get(b) ?? b;
  return ca === cb;
}

/**
 * For each decoration tile in a TransferLocation, detect which cardinal neighbours
 * contain the same decoration type (with equivalences) and write results to
 * `location.decorationSide`.
 *
 * Side numbering: 1 - north, 2 - east, 3 - south, 4 - west
 */
export function declareDecorationSides(location: TransferLocation): void {
  if (!location) return;

  const decorations = location.decorations || {};
  const eqLookup = buildEquivalenceLookup();

  // Типы, для которых стороны определяются только относительно wallLike
  const sideToWall = new Set<string>([
    LocationDecorations.POSTER,
    LocationDecorations.LOCKER,
    LocationDecorations.SAFE,
    LocationDecorations.FRIDGE,
    LocationDecorations.OVEN,
    LocationDecorations.TV,
    LocationDecorations.TOILET,
    LocationDecorations.SINK,
    LocationDecorations.FIREPLACE,
    LocationDecorations.VENDINGMACHINE,
    LocationDecorations.TERMINAL,
    LocationDecorations.CASHREGISTER,
    LocationDecorations.BLACKBOARD,
    LocationDecorations.FIRSTAID,
    LocationDecorations.MILITARYCONTAINER,
    LocationDecorations.WEAPONRACK,

  ]);
  // Какие декорации считаются стеноподобными
  const wallLike = new Set<string>([
    LocationDecorations.WALL,
    LocationDecorations.FENCE,
    LocationDecorations.PARTITION,

  ]);

  // Build map: "x,y" -> array of decoration keys present at that cell
  const posMap = new Map<string, string[]>();
  for (const decoKey of Object.keys(decorations)) {
    const points = (decorations as Record<string, [number, number]>)[decoKey] || [];
    for (const p of points) {
      const key = `${p[0]},${p[1]}`;
      const arr = posMap.get(key) || [];
      if (!arr.includes(decoKey)) arr.push(decoKey);
      posMap.set(key, arr);
    }
  }

  // Cardinal deltas with side numbers
  const deltas: Array<{ dx: number; dy: number; side: number }> = [
    { dx: 0, dy: -1, side: 1 }, // north
    { dx: 1, dy: 0, side: 2 }, // east
    { dx: 0, dy: 1, side: 3 }, // south
    { dx: -1, dy: 0, side: 4 } // west
  ];

  const result: PointWithNumber[] = [];

  for (const [posKey, typesHere] of posMap.entries()) {
    const [sxStr, syStr] = posKey.split(',');
    const sx = Number(sxStr);
    const sy = Number(syStr);
    const sides = new Set<number>();

    // Если среди типов есть хотя бы один из sideToWall — применяем особую логику
    const useWallLike = typesHere.some(t => sideToWall.has(t));

    for (const d of deltas) {
      const nx = sx + d.dx;
      const ny = sy + d.dy;

      // bounds check if locationSize present
      if (location.locationSize) {
        const maxX = location.locationSize[0];
        const maxY = location.locationSize[1];
        if (nx < 0 || ny < 0 || nx >= maxX || ny >= maxY) continue;
      }

      const neighKey = `${nx},${ny}`;
      const neighTypes = posMap.get(neighKey) || [];

      if (useWallLike) {
        // Для постеров и т.п. — только если рядом wallLike
        if (neighTypes.some(nt => wallLike.has(nt))) {
          sides.add(d.side);
        }
      } else {
        // Обычная логика эквивалентности
        let matched = false;
        for (const t of typesHere) {
          for (const nt of neighTypes) {
            if (areEquivalent(t, nt, eqLookup)) { matched = true; break; }
          }
          if (matched) { sides.add(d.side); break; }
        }
      }
    }

    // Convert set to sorted array
    const sidesArr = Array.from(sides).sort((a, b) => a - b);
    result.push([sx, sy, sidesArr]);
  }

  location.decorationSide = result;
}

export default declareDecorationSides;


