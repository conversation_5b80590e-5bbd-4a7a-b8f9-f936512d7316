import { LocationDecorations, WorldMapDecorations } from '../../../shared/enums';


// Пути к текстурам декораций
// Ключи соответствуют значениям enum WorldMapDecorations
export const DECORATION_TEXTURES: Partial<Record<WorldMapDecorations, string[]>> = {
  [WorldMapDecorations.FOREST]: [
    '/textures/worldMap/decorations/forest/forest_1.png',
    '/textures/worldMap/decorations/forest/forest_2.png',
    '/textures/worldMap/decorations/forest/forest_3.png',
    '/textures/worldMap/decorations/forest/forest_4.png',
  ],
  [WorldMapDecorations.MOUNTAINS]: [
    '/textures/worldMap/decorations/mountains/1.png',
    '/textures/worldMap/decorations/mountains/2.png',
    '/textures/worldMap/decorations/mountains/3.png',
    '/textures/worldMap/decorations/mountains/4.png',
  ],
  [WorldMapDecorations.ROAD]: [
    '/textures/worldMap/decorations/roads/1.png',
    
    '/textures/worldMap/decorations/roads/2.png',
    '/textures/worldMap/decorations/roads/3.png',
    '/textures/worldMap/decorations/roads/4.png',
  ],
  [WorldMapDecorations.SWAMP]: [
      '/textures/worldMap/decorations/swamp/1.png',
      '/textures/worldMap/decorations/swamp/2.png',
      '/textures/worldMap/decorations/swamp/3.png',
      '/textures/worldMap/decorations/swamp/4.png',
  ],
  [WorldMapDecorations.CITY]: [
      '/textures/worldMap/decorations/city/1.png',
      '/textures/worldMap/decorations/city/2.png',
      '/textures/worldMap/decorations/city/3.png',
      '/textures/worldMap/decorations/city/4.png',
  ],
  [WorldMapDecorations.RUINS]: [
      '/textures/worldMap/decorations/ruins/1.png',
      '/textures/worldMap/decorations/ruins/2.png',
      '/textures/worldMap/decorations/ruins/3.png',
      '/textures/worldMap/decorations/ruins/4.png',
  ],
  [WorldMapDecorations.LAKE]: [
      '/textures/worldMap/decorations/lake/1/1.png',
      '/textures/worldMap/decorations/lake/1/2.png',
      '/textures/worldMap/decorations/lake/1/3.png',
      '/textures/worldMap/decorations/lake/1/4.png',
  ],
  [WorldMapDecorations.RUBBLE]: [
      '/textures/worldMap/decorations/rubble/1.png',
      '/textures/worldMap/decorations/rubble/2.png',
      '/textures/worldMap/decorations/rubble/3.png',
      '/textures/worldMap/decorations/rubble/4.png',
  ],
  [WorldMapDecorations.BRIDGE]: [
      '/textures/worldMap/decorations/bridge/bridge_1.png',
  ],
  [WorldMapDecorations.BUSHES]: [
      '/textures/worldMap/decorations/bushes/1.png',
      
      '/textures/worldMap/decorations/bushes/2.png',
      '/textures/worldMap/decorations/bushes/3.png',
      '/textures/worldMap/decorations/bushes/4.png',
  ]
};

export const DECORATION_LOCATION_TEXTURES: Partial<Record<LocationDecorations, string[]>> = {
  [LocationDecorations.ROCKS]: [
    '/textures/Location/decorations/rocks/1.png',
    '/textures/Location/decorations/rocks/2.png',
    '/textures/Location/decorations/rocks/3.png',
    '/textures/Location/decorations/rocks/4.png',
    '/textures/Location/decorations/rocks/5.png',
    '/textures/Location/decorations/rocks/6.png',
    '/textures/Location/decorations/rocks/7.png',
    '/textures/Location/decorations/rocks/8.png', 
  ],
  [LocationDecorations.BOX]: [
    '/textures/Location/decorations/box/1.png',
    '/textures/Location/decorations/box/2.png',
    '/textures/Location/decorations/box/3.png',
    '/textures/Location/decorations/box/4.png',
    '/textures/Location/decorations/box/5.png',
    '/textures/Location/decorations/box/6.png',
    '/textures/Location/decorations/box/7.png',
    '/textures/Location/decorations/box/8.png',
  ],
  [LocationDecorations.TIRE]: [
    '/textures/Location/decorations/tire/1.png',
    '/textures/Location/decorations/tire/2.png',
    '/textures/Location/decorations/tire/3.png',
    '/textures/Location/decorations/tire/4.png',  
    '/textures/Location/decorations/tire/5.png',
    '/textures/Location/decorations/tire/6.png',
    '/textures/Location/decorations/tire/7.png',
    '/textures/Location/decorations/tire/8.png',
  ],
  [LocationDecorations.LITTER]: [
    '/textures/Location/decorations/litter/1.png',
    '/textures/Location/decorations/litter/2.png',
    '/textures/Location/decorations/litter/3.png',
    '/textures/Location/decorations/litter/4.png',
  ],
  [LocationDecorations.SIGN]: [
    '/textures/Location/decorations/sign/1.png',
    '/textures/Location/decorations/sign/2.png',
    '/textures/Location/decorations/sign/3.png',
    '/textures/Location/decorations/sign/4.png',
    '/textures/Location/decorations/sign/5.png',
    '/textures/Location/decorations/sign/6.png',
    '/textures/Location/decorations/sign/7.png',
    '/textures/Location/decorations/sign/8.png'

  ],
  [LocationDecorations.PUDDLE]: [
    '/textures/Location/decorations/puddle/1.png',
    '/textures/Location/decorations/puddle/2.png',
    '/textures/Location/decorations/puddle/3.png',
    '/textures/Location/decorations/puddle/4.png',
    '/textures/Location/decorations/puddle/5.png',
    '/textures/Location/decorations/puddle/6.png',
    '/textures/Location/decorations/puddle/7.png',
    '/textures/Location/decorations/puddle/8.png',

  ],
  [LocationDecorations.MUD]: [
    '/textures/Location/decorations/mud/1.png',
    '/textures/Location/decorations/mud/2.png',
    '/textures/Location/decorations/mud/3.png',
    '/textures/Location/decorations/mud/4.png',
    '/textures/Location/decorations/mud/5.png',
    '/textures/Location/decorations/mud/6.png',
    '/textures/Location/decorations/mud/7.png',
    '/textures/Location/decorations/mud/8.png',
  ],
 
  
    [LocationDecorations.LOG]: [
    '/textures/Location/decorations/log/1.png',
    '/textures/Location/decorations/log/2.png',
    '/textures/Location/decorations/log/3.png',
    '/textures/Location/decorations/log/4.png',
    '/textures/Location/decorations/log/5.png',
    '/textures/Location/decorations/log/6.png',
    '/textures/Location/decorations/log/7.png',
    '/textures/Location/decorations/log/8.png',
  ],
  [LocationDecorations.TREE]: [
    '/textures/Location/decorations/tree/1.png',
    '/textures/Location/decorations/tree/2.png',
    '/textures/Location/decorations/tree/3.png',
    '/textures/Location/decorations/tree/4.png',
    '/textures/Location/decorations/tree/5.png',
    '/textures/Location/decorations/tree/6.png',
    '/textures/Location/decorations/tree/7.png',
    '/textures/Location/decorations/tree/8.png',
  ],
  [LocationDecorations.BUSH]: [
    '/textures/Location/decorations/bush/1.png',
    '/textures/Location/decorations/bush/2.png',
    '/textures/Location/decorations/bush/3.png',
    '/textures/Location/decorations/bush/4.png',
    '/textures/Location/decorations/bush/5.png',
    '/textures/Location/decorations/bush/6.png',
    '/textures/Location/decorations/bush/7.png',
    '/textures/Location/decorations/bush/8.png',
  ],
  [LocationDecorations.GRASS]: [
    '/textures/Location/decorations/grass/1.png',
    '/textures/Location/decorations/grass/2.png',
    '/textures/Location/decorations/grass/3.png',
    '/textures/Location/decorations/grass/4.png',
    '/textures/Location/decorations/grass/5.png',
    '/textures/Location/decorations/grass/6.png',
    '/textures/Location/decorations/grass/7.png',
    '/textures/Location/decorations/grass/8.png',

  ],
  [LocationDecorations.WATER]: [
    '/textures/Location/decorations/water/1.png',
    '/textures/Location/decorations/water/2.png',
    '/textures/Location/decorations/water/3.png',
    '/textures/Location/decorations/water/4.png',
  ],
  [LocationDecorations.FENCE]: [
    '/textures/Location/decorations/fence/1.png',
    '/textures/Location/decorations/fence/2.png',
    '/textures/Location/decorations/fence/3.png',
    '/textures/Location/decorations/fence/4.png',
  ],
  
  [LocationDecorations.ROAD]: [
    '/textures/Location/decorations/road/1.png',
    '/textures/Location/decorations/road/2.png',
    '/textures/Location/decorations/road/3.png',
    '/textures/Location/decorations/road/4.png',
    '/textures/Location/decorations/road/5.png',
    '/textures/Location/decorations/road/6.png',
    '/textures/Location/decorations/road/7.png',
    '/textures/Location/decorations/road/8.png',
  ],
  
  [LocationDecorations.BARREL]: [
    '/textures/Location/decorations/barrel/1.png',
    '/textures/Location/decorations/barrel/2.png',
    '/textures/Location/decorations/barrel/3.png',
    '/textures/Location/decorations/barrel/4.png',
  ],
  [LocationDecorations.STREETLIGHT]: [
    '/textures/Location/decorations/streetlight/1.png',
    '/textures/Location/decorations/streetlight/2.png',
    '/textures/Location/decorations/streetlight/3.png',
    '/textures/Location/decorations/streetlight/4.png',
  ],
  [LocationDecorations.CAR]: [
    '/textures/Location/decorations/car/1.png',
    '/textures/Location/decorations/car/2.png',
    '/textures/Location/decorations/car/3.png',
    '/textures/Location/decorations/car/4.png',
  ],
  [LocationDecorations.FURNITURE]: [
    '/textures/Location/decorations/furniture/1.png',
    '/textures/Location/decorations/furniture/2.png',
    '/textures/Location/decorations/furniture/3.png',
    '/textures/Location/decorations/furniture/4.png',
  ],
  

}