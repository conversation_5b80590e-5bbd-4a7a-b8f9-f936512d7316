import { DecorationZoneType, LocationSubtype } from '../../../shared/enums';

export interface HouseRoomSettings {
  type: string;
  xyMin: number;
  xyMax: number;
  door: number;
  smallerRooms: number;
  size: 1 | 2 | 3; // 1 - маленький, 2 - средний, 3 - большой
  room: boolean; // является ли комнатой (может быть внутри здания)
  building: boolean; // является ли зданием (может быть отдельно стоящим)
  decorationZoneType: DecorationZoneType;
  canBeUsed: LocationSubtype[] | 'all'; // типы локаций где может использоваться
  required: boolean; // обязательное здание для данного типа локации
}
//// ТОЛЬКО ДЛЯ РАНДОМНОГО ГЕНЕРАТОРА НЕ ДЛЯ ПРЕСЕТА
export const houseRoomSettings: HouseRoomSettings[] = [
  // Медицинские помещения
  {
    type: 'firstAid',
    xyMin: 7,
    xyMax: 9,
    door: 1,
    smallerRooms: 1,
    size: 2,
    room: true,
    building: true,
    decorationZoneType: DecorationZoneType.HOSPITAL,
    canBeUsed: [LocationSubtype.TOWN, LocationSubtype.VILLAGE, LocationSubtype.LABORATORY, LocationSubtype.MILITARY, LocationSubtype.FACTORY],
    required: false
  },
  {
    type: 'hospital',
    xyMin: 22,
    xyMax: 30,
    door: 2,
    smallerRooms: 7,
    size: 3,
    room: false,
    building: true,
    decorationZoneType: DecorationZoneType.HOSPITAL,
    canBeUsed: [ LocationSubtype.HOSPITAL],
    required: true
  },

  // Санитарные помещения
  {
    type: 'toilet',
    xyMin: 4,
    xyMax: 6,
    door: 1,
    smallerRooms: 0,
    size: 1,
    room: true,
    building: true,
    decorationZoneType: DecorationZoneType.BATHROOM,
    canBeUsed: 'all',
    required: false
  },
  {
    type: 'bathroom',
    xyMin: 6,
    xyMax: 8,
    door: 1,
    smallerRooms: 0,
    size: 1,
    room: true,
    building: false,
    decorationZoneType: DecorationZoneType.BATHROOM,
    canBeUsed: 'all',
    required: false
  },

  // Торговые помещения
  {
    type: 'pantry',
    xyMin: 4,
    xyMax: 5,
    door: 1,
    smallerRooms: 0,
    size: 1,
    room: true,
    building: false,
    decorationZoneType: DecorationZoneType.SHOP,
    canBeUsed: 'all',
    required: false
  },
  {
    type: 'gunShop',
    xyMin: 7,
    xyMax: 12,
    door: 1,
    smallerRooms: 1,
    size: 2,
    room: false,
    building: true,
    decorationZoneType: DecorationZoneType.SHOP,
    canBeUsed: [LocationSubtype.TOWN, LocationSubtype.SHOP,  LocationSubtype.VILLAGE],
    required: false
  },
  {
    type: 'shop',
    xyMin: 12,
    xyMax: 21,
    door: 1,
    smallerRooms: 4,
    size: 2,
    room: false,
    building: true,
    decorationZoneType: DecorationZoneType.SHOP,
    canBeUsed: [LocationSubtype.TOWN, LocationSubtype.VILLAGE, LocationSubtype.SHOP],
    required: true
  },

  // Жилые помещения
  {
    type: 'livingRoom',
    xyMin: 8,
    xyMax: 12,
    door: 1,
    smallerRooms: 0,
    size: 2,
    room: true,
    building: false,
    decorationZoneType: DecorationZoneType.VILLAGE,
    canBeUsed: 'all',
    required: false
  },
  {
    type: 'bedroom',
    xyMin: 6,
    xyMax: 10,
    door: 1,
    smallerRooms: 0,
    size: 1,
    room: true,
    building: false,
    decorationZoneType: DecorationZoneType.VILLAGE,
    canBeUsed: 'all',
    required: false
  },
  {
    type: 'livingHouseS',
    xyMin: 10,
    xyMax: 14,
    door: 1,
    smallerRooms: 2,
    size: 2,
    room: false,
    building: true,
    decorationZoneType: DecorationZoneType.VILLAGE,
    canBeUsed: [LocationSubtype.TOWN, LocationSubtype.CAMP, LocationSubtype.FARM, LocationSubtype.VILLAGE],
    required: false
  },
  {
    type: 'livingHouseM',
    xyMin: 14,
    xyMax: 16,
    door: 2,
    smallerRooms: 3,
    size: 3,
    room: false,
    building: true,
    decorationZoneType: DecorationZoneType.VILLAGE,
    canBeUsed: [LocationSubtype.TOWN, LocationSubtype.CAMP, LocationSubtype.FARM, LocationSubtype.VILLAGE],
    required: false
  },
  {
    type: 'livingHouseL',
    xyMin: 14,
    xyMax: 21,
    door: 2,
    smallerRooms: 4,
    size: 3,
    room: false,
    building: true,
    decorationZoneType: DecorationZoneType.VILLAGE,
    canBeUsed: [LocationSubtype.TOWN, LocationSubtype.CAMP, LocationSubtype.FARM, LocationSubtype.VILLAGE],
    required: false
  },

  // Военные объекты
  {
    type: 'armory',
    xyMin: 8,
    xyMax: 12,
    door: 1,
    smallerRooms: 1,
    size: 2,
    room: true,
    building: true,
    decorationZoneType: DecorationZoneType.MILITARY,
    canBeUsed: [LocationSubtype.MILITARY, LocationSubtype.BUNKER, LocationSubtype.POLICE],
    required: false
  },
  {
    type: 'barracks',
    xyMin: 18,
    xyMax: 24,
    door: 2,
    smallerRooms: 2,
    size: 3,
    room: false,
    building: true,
    decorationZoneType: DecorationZoneType.MILITARY,
    canBeUsed: [LocationSubtype.MILITARY, LocationSubtype.BUNKER],
    required: true
  },
  {
    type: 'guardPost',
    xyMin: 6,
    xyMax: 8,
    door: 1,
    smallerRooms: 0,
    size: 1,
    room: true,
    building: true,
    decorationZoneType: DecorationZoneType.MILITARY,
    canBeUsed: [LocationSubtype.MILITARY, LocationSubtype.BUNKER, LocationSubtype.POLICE],
    required: false
  },

  // Промышленные объекты
  {
    type: 'workshop',
    xyMin: 10,
    xyMax: 14,
    door: 1,
    smallerRooms: 1,
    size: 2,
    room: true,
    building: true,
    decorationZoneType: DecorationZoneType.FACTORY,
    canBeUsed: [LocationSubtype.FACTORY, LocationSubtype.MILITARY, LocationSubtype.CAMP],
    required: false
  },
  {
    type: 'laboratory',
    xyMin: 18,
    xyMax: 24,
    door: 1,
    smallerRooms: 2,
    size: 3,
    room: false,
    building: true,
    decorationZoneType: DecorationZoneType.LABORATORY,
    canBeUsed: [LocationSubtype.LABORATORY, LocationSubtype.FACTORY, LocationSubtype.BUNKER],
    required: true
  },
  {
    type: 'storage',
    xyMin: 6,
    xyMax: 10,
    door: 1,
    smallerRooms: 0,
    size: 1,
    room: true,
    building: true,
    decorationZoneType: DecorationZoneType.FACTORY,
    canBeUsed: 'all',
    required: false
  },

  // Сельскохозяйственные объекты
  {
    type: 'barn',
    xyMin: 18,
    xyMax: 27,
    door: 2,
    smallerRooms: 1,
    size: 3,
    room: false,
    building: true,
    decorationZoneType: DecorationZoneType.FARM,
    canBeUsed: [LocationSubtype.FARM, LocationSubtype.VILLAGE],
    required: true
  },
  {
    type: 'greenhouse',
    xyMin: 8,
    xyMax: 12,
    door: 1,
    smallerRooms: 0,
    size: 2,
    room: false,
    building: true,
    decorationZoneType: DecorationZoneType.FARM,
    canBeUsed: [LocationSubtype.FARM, LocationSubtype.VILLAGE],
    required: false
  },

  // Общественные здания
  {
    type: 'school',
    xyMin: 21,
    xyMax: 30,
    door: 2,
    smallerRooms: 4,
    size: 3,
    room: false,
    building: true,
    decorationZoneType: DecorationZoneType.SCHOOL,
    canBeUsed: [LocationSubtype.SCHOOL],
    required: true
  },
  {
    type: 'hotel',
    xyMin: 18,
    xyMax: 27,
    door: 2,
    smallerRooms: 3,
    size: 3,
    room: false,
    building: true,
    decorationZoneType: DecorationZoneType.HOTEL,
    canBeUsed: [LocationSubtype.TOWN, LocationSubtype.HOTEL],
    required: true
  },
  {
    type: 'bar',
    xyMin: 8,
    xyMax: 12,
    door: 1,
    smallerRooms: 1,
    size: 2,
    room: false,
    building: true,
    decorationZoneType: DecorationZoneType.BAR,
    canBeUsed: [LocationSubtype.TOWN, LocationSubtype.VILLAGE, LocationSubtype.CAMP],
    required: false
  },

  // Заправочные станции
  {
    type: 'gasStation',
    xyMin: 15,
    xyMax: 21,
    door: 1,
    smallerRooms: 1,
    size: 2,
    room: false,
    building: true,
    decorationZoneType: DecorationZoneType.GASSTATION,
    canBeUsed: [LocationSubtype.GASSTATION],
    required: true
  },

  // Полицейские участки
  {
    type: 'policeStation',
    xyMin: 18,
    xyMax: 24,
    door: 2,
    smallerRooms: 2,
    size: 3,
    room: false,
    building: true,
    decorationZoneType: DecorationZoneType.POLICE,
    canBeUsed: [LocationSubtype.POLICE],
    required: true
  },

  // Метро
  {
    type: 'subwayStation',
    xyMin: 24,
    xyMax: 33,
    door: 3,
    smallerRooms: 2,
    size: 3,
    room: false,
    building: true,
    decorationZoneType: DecorationZoneType.SUBWAY,
    canBeUsed: [LocationSubtype.SUBWAY],
    required: true
  },

  // Бункеры
  {
    type: 'bunkerRoom',
    xyMin: 8,
    xyMax: 12,
    door: 1,
    smallerRooms: 1,
    size: 2,
    room: true,
    building: true,
    decorationZoneType: DecorationZoneType.BUNKER,
    canBeUsed: [LocationSubtype.BUNKER],
    required: false
  },
  {
    type: 'bunkerComplex',
    xyMin: 40,
    xyMax: 50,
    door: 2,
    smallerRooms: 15,
    size: 3,
    room: false,
    building: true,
    decorationZoneType: DecorationZoneType.BUNKER,
    canBeUsed: [LocationSubtype.BUNKER],
    required: true
  }
];
