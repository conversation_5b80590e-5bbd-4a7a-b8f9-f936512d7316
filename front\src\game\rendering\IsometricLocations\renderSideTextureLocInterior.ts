/**
 * Система отрисовки текстур декораций с поддержкой decorationSides
 */

import { LocationDecorations, MaterialTexture, LocationSubtype } from '../../../shared/enums';
import { loadLocationTexture } from '../textures/locationTextures';
import { DECORATION_TEXTURE_SETTINGS } from '../../utils/constants/renderingLocation';
import { DECORATION_FADE } from '../../utils/constants/timeLight';

// Кэш загруженных текстур для decorationSides
const sideTextureCache = new Map<string, HTMLImageElement>();

// Типы декораций, к которым применима эта логика (удобно расширять)
const SUPPORTED_SIDE_DECORATIONS: LocationDecorations[] = [
  LocationDecorations.WALL,
  LocationDecorations.WINDOW,
  LocationDecorations.DOOR,
];

// Подтипы локаций, которые используют уникальные папки вместо материалов
const UNIQUE_SUBTYPE_DECORATIONS: Record<LocationSubtype, LocationDecorations[]> = {
  [LocationSubtype.SUBWAY]: [LocationDecorations.WINDOW],
  [LocationSubtype.POLICE]: [LocationDecorations.WALL, LocationDecorations.WINDOW, LocationDecorations.DOOR],
  [LocationSubtype.SCHOOL]: [LocationDecorations.WALL, LocationDecorations.WINDOW, LocationDecorations.DOOR],
  [LocationSubtype.HOSPITAL]: [LocationDecorations.WALL, LocationDecorations.WINDOW, LocationDecorations.DOOR],
  [LocationSubtype.LABORATORY]: [LocationDecorations.WALL, LocationDecorations.WINDOW, LocationDecorations.DOOR],
  [LocationSubtype.MILITARY]: [LocationDecorations.WALL, LocationDecorations.WINDOW, LocationDecorations.DOOR],
  [LocationSubtype.BUNKER]: [LocationDecorations.WALL, LocationDecorations.WINDOW, LocationDecorations.DOOR],
  [LocationSubtype.FACTORY]: [LocationDecorations.WALL, LocationDecorations.WINDOW, LocationDecorations.DOOR],
  [LocationSubtype.GASSTATION]: [LocationDecorations.WALL, LocationDecorations.WINDOW, LocationDecorations.DOOR],
  // Остальные подтипы используют стандартную логику с материалами
  [LocationSubtype.HOTEL]: [],

  [LocationSubtype.TOWN]: [],
  [LocationSubtype.VILLAGE]: [],
  [LocationSubtype.CAMP]: [],
  [LocationSubtype.SHOP]: [],
  [LocationSubtype.FARM]: [],
  [LocationSubtype.OTHER]: [],

};

// Ожидаемое максимальное число вариаций по умолчанию (если не знаем точное количество файлов)
const DEFAULT_VARIATION_COUNT = 6;

// Константы для затемнения текстур (из renderUtilsLocInterior.ts)
const DARKNESS_OPACITY = {
  MIN_VISIBILITY: 0.1, // Минимальная видимость в полной темноте
  CALCULATE_ALPHA: (darknessLevel: number) => Math.max(DARKNESS_OPACITY.MIN_VISIBILITY, 1 - darknessLevel)
} as const;



/**
 * Проверяет, нужно ли использовать уникальную папку подтипа вместо материала
 */
function shouldUseUniqueSubtype(decoration: LocationDecorations, locationSubtype?: LocationSubtype): boolean {
  if (!locationSubtype) return false;

  const uniqueDecorations = UNIQUE_SUBTYPE_DECORATIONS[locationSubtype];
  return uniqueDecorations && uniqueDecorations.includes(decoration);
}

/**
 * Загружает текстуру стороны декорации и кэширует её
 */
async function loadSideTexture(src: string): Promise<HTMLImageElement> {
  // Проверяем кэш
  if (sideTextureCache.has(src)) {
    return sideTextureCache.get(src)!;
  }

  // Загружаем новую текстуру
  return new Promise((resolve, reject) => {
    const img = new Image();
    img.onload = () => {
      sideTextureCache.set(src, img);
      resolve(img);
    };
    img.onerror = () => {
      console.warn(`Не удалось загрузить текстуру стороны декорации: ${src}`);
      // Создаем заглушку
      const placeholder = new Image(1, 1);
      sideTextureCache.set(src, placeholder);
      resolve(placeholder);
    };
    img.src = src;
  });
}

/**
 * Получает пути к текстурам для decorationSides
 */
export function getSideTexturePaths(
  decoration: LocationDecorations,
  decorationSides: number[],
  locationData: any,
  material?: MaterialTexture
): string[] {
  // Поддерживаем несколько типов (wall, window, door и др.)
  if (!SUPPORTED_SIDE_DECORATIONS.includes(decoration)) {
    return [];
  }

  // Ожидаемый формат decorationSides: [x, y, number[]]
  if (!(decorationSides && decorationSides.length > 2 && Array.isArray(decorationSides[2]))) {
    return [];
  }

  const textureNumbers = decorationSides[2] as number[];
  if (!textureNumbers || textureNumbers.length === 0) return [];

  // Сортируем номера направлений, чтобы получить стабильное имя папки, например "1_3_4"
  let comboKey = textureNumbers.slice().sort((a, b) => a - b).join('_');
  if (textureNumbers.length === 1) {
    const only = textureNumbers[0];
    if (only === 1 || only === 3) comboKey = '1_3';
    else if (only === 2 || only === 4) comboKey = '2_4';
  }

  const textureMaterial = locationData?.textureMaterial ?? material ?? undefined;
  // Normalize subtype to string lowercase when possible — server/transfer data may provide different casing or formats
  const rawLocationSubtype = locationData?.subtype;
  const locationSubtype = typeof rawLocationSubtype === 'string'
    ? (rawLocationSubtype.toLowerCase() as LocationSubtype)
    : (rawLocationSubtype as LocationSubtype | undefined);

  // Проверяем, нужно ли использовать уникальную папку подтипа
  const useUniqueSubtype = shouldUseUniqueSubtype(decoration, locationSubtype);

  // Для дверей поддерживаем отдельную папку открытого/закрытого состояния
  let openCloseFolder: string | undefined;
  if (decoration === LocationDecorations.DOOR) {
    // Пробуем найти флаг isOpen в разных местах структуры interactive
    const isOpen = Boolean(
      (locationData as any)?.interactive?.type?.isOpen ?? (locationData as any)?.interactive?.isOpen ?? false
    );
    openCloseFolder = isOpen ? 'open' : 'closed';
  }

  // Выбор детерминированной вариации на основе координат (чтобы при перерисовке не менялось)
  const isoX = Number(decorationSides[0]) || 0;
  const isoY = Number(decorationSides[1]) || 0;

  const variation = chooseDeterministicVariation(isoX, isoY, comboKey, DEFAULT_VARIATION_COUNT);

  const paths: string[] = [];

  // Если используем уникальный подтип, добавляем пути с папкой подтипа
  if (useUniqueSubtype && locationSubtype) {
    if (openCloseFolder) {
      // двери: подтип/комбо/открытость/вариация
      paths.push(
        `/textures/Location/decorations/${decoration}/${locationSubtype}/${comboKey}/${openCloseFolder}/${variation}.png`
      );
    }
    // fallback без папки open/closed для подтипа
    paths.push(`/textures/Location/decorations/${decoration}/${locationSubtype}/${comboKey}/${variation}.png`);
  }

  // Стандартная логика с материалом (как fallback или основной путь)
  if (textureMaterial) {
    if (openCloseFolder) {
      // двери: материал/комбо/открытость/вариация
      paths.push(
        `/textures/Location/decorations/${decoration}/${textureMaterial}/${comboKey}/${openCloseFolder}/${variation}.png`
      );
    }
    // fallback без папки open/closed
    paths.push(`/textures/Location/decorations/${decoration}/${textureMaterial}/${comboKey}/${variation}.png`);
  }

  // Try combo without material (some older folders may be structured like that)
  if (openCloseFolder) {
    paths.push(`/textures/Location/decorations/${decoration}/${comboKey}/${openCloseFolder}/${variation}.png`);
  }
  paths.push(`/textures/Location/decorations/${decoration}/${comboKey}/${variation}.png`);


  return paths;
}

/**
 * Выбирает детерминированную вариацию по координатам и ключу комбинации
 */
function chooseDeterministicVariation(x: number, y: number, comboKey: string, maxVariations: number): number {
  // Простая детерминированная хеш-функция с использованием π и комбинации
  const seed = comboKey.split('_').reduce((acc, v) => acc + Number(v || 0), 0);
  const p = (x * Math.PI * 1.414 + y * Math.PI * 1.732 + seed * 0.618);
  const hash = Math.abs(Math.sin(p * 2.236) * Math.cos(p / 1.618) * 10000);
  const index = Math.floor(hash) % maxVariations;
  return index + 1; // файлы нумеруются с 1.png
}

/**
 * Загружает все текстуры для decorationSides
 */
export async function loadSideTextures(
  decoration: LocationDecorations,
  decorationSides: number[],
  locationData: any,
  material?: MaterialTexture
): Promise<HTMLImageElement[]> {
  const texturePaths = getSideTexturePaths(decoration, decorationSides, locationData, material);

  // Пробуем пути последовательно и возвращаем первую успешно загруженную текстуру
  for (const path of texturePaths) {
    try {
      // Если в кеше уже есть и загружено — возвращаем его
      const cached = sideTextureCache.get(path);
      if (cached && cached.complete) {
        return [cached];
      }

      const img = await loadSideTexture(path);
      // Проверяем, действительно ли картинка загрузилась (а не placeholder)
      if (img && img.complete && img.naturalWidth && img.naturalHeight) {
        return [img];
      }
      // иначе — пробуем следующий путь
    } catch (err) {
      // loadSideTexture никогда не должен бросать (он резолвит placeholder), но на всякий случай
      console.warn('Проблема при загрузке texture path:', path, err);
    }
  }

  return [];
}

/**
 * Синхронно получает загруженные текстуры для decorationSides
 */
export function getLoadedSideTextures(
  decoration: LocationDecorations,
  decorationSides: number[],
  locationData: any,
  material?: MaterialTexture
): HTMLImageElement[] {
  const texturePaths = getSideTexturePaths(decoration, decorationSides, locationData, material);

  // Возвращаем только загруженные (и полностью загруженные) изображения — порядок важен
  return texturePaths
    .map(path => sideTextureCache.get(path))
    .filter((img): img is HTMLImageElement => img !== undefined && img.complete);
}

/**
 * Отрисовывает текстуры decorationSides на тайле
 */
export function drawSideTextures(
  ctx: CanvasRenderingContext2D,
  centerX: number,
  centerY: number,
  decoration: LocationDecorations,
  decorationSides: number[],
  locationData: any,
  material?: MaterialTexture,
  darknessLevel: number = 0,
  playerPosition: { x: number; y: number } | null = null,
  tilePos: { x: number; y: number } | null = null
): void {
  // Мы ожидаем один итоговый файл для комбинации направлений — берём первый загруженный путь
  const loaded = getLoadedSideTextures(decoration, decorationSides, locationData, material);

  if (loaded.length === 0) {
    loadSideTextures(decoration, decorationSides, locationData, material);
    return;
  }

  const texture = loaded[0];
  if (!texture || !texture.complete) return;

  ctx.save();
  // Базовая alpha из расчёта затемнения
  let alpha = 1;
  if (darknessLevel > 0) {
    alpha = DARKNESS_OPACITY.CALCULATE_ALPHA(darknessLevel);
  }

  // Если есть позиция игрока и координаты тайла — применяем подсветку ТОЛЬКО для указанных относительных координат
  if (playerPosition && tilePos) {
    const dx = tilePos.x - playerPosition.x;
    const dy = tilePos.y - playerPosition.y;
    // Разрешенные относительные позиции для подсветки: {-1,0}, {-1,1}, {0,1}
    const shouldHighlight = (dx === 1 && dy === 0) || (dx === 1 && dy === 1) || (dx === 0 && dy === 1) || (dx === 2 && dy === 1) || (dx === 2 && dy === 2) || (dx === 1 && dy === 2);
    if (shouldHighlight) {
      // Уменьшаем alpha до минимальной если нужно показать персонажа
      alpha = Math.min(alpha, DECORATION_FADE.MIN_ALPHA);
    }
  }

  ctx.globalAlpha = alpha;

  // Вычисляем размеры текстуры с учетом настроек из констант
  let drawW = texture.width;
  let drawH = texture.height;

  if (DECORATION_TEXTURE_SETTINGS.ENABLE_SCALING) {
    const scaleMultiplier = DECORATION_TEXTURE_SETTINGS.SCALE_MULTIPLIERS.WALL || 1.0;

    drawW = DECORATION_TEXTURE_SETTINGS.DEFAULT_WIDTH * scaleMultiplier;
    drawH = DECORATION_TEXTURE_SETTINGS.DEFAULT_HEIGHT * scaleMultiplier;

    if (DECORATION_TEXTURE_SETTINGS.PRESERVE_ASPECT) {
      const aspectRatio = texture.width / texture.height;
      if (aspectRatio > 1) {
        drawH = drawW / aspectRatio;
      } else {
        drawW = drawH * aspectRatio;
      }
    }
  }

  let offsetX = 0;
  let offsetY = 0;
  if (DECORATION_TEXTURE_SETTINGS.ENABLE_OFFSET) {
    offsetX = DECORATION_TEXTURE_SETTINGS.HORIZONTAL_OFFSET.WALL || 0;
    offsetY = DECORATION_TEXTURE_SETTINGS.VERTICAL_OFFSET.WALL || 0;
  }

  ctx.drawImage(texture, centerX - drawW / 2 + offsetX, centerY - drawH / 2 + offsetY, drawW, drawH);
  ctx.restore();
}

/**
 * Предзагружает текстуры для всех возможных комбинаций decorationSides
 */
export async function preloadSideTextures(): Promise<void> {
  const materials = [MaterialTexture.BETON, MaterialTexture.WOOD, MaterialTexture.METAL, MaterialTexture.BRICK];
  const folders = [1]; // Пока что только папка 1
  const sideNumbers = [1, 2, 3, 4]; // Возможные номера текстур

  const allTexturePaths: string[] = [];

  // Генерируем возможные комбинации направлений (1..4) в виде подпапок: "1_3_4" и т.д.
  const combos: string[] = [];
  const n = sideNumbers.length;
  // простая генерация всех непустых подмножеств
  for (let mask = 1; mask < (1 << n); mask++) {
    const parts: number[] = [];
    for (let i = 0; i < n; i++) {
      if (mask & (1 << i)) parts.push(sideNumbers[i]);
    }
    combos.push(parts.join('_'));
  }

  // Для каждого поддерживаемого типа создаём пути с материалами + комбинациями
  SUPPORTED_SIDE_DECORATIONS.forEach(decType => {
    const isDoor = decType === LocationDecorations.DOOR;

    // Добавляем пути для уникальных подтипов
    Object.entries(UNIQUE_SUBTYPE_DECORATIONS).forEach(([subtype, decorations]) => {
      if (decorations.includes(decType)) {
        combos.forEach(combo => {
          for (let v = 1; v <= DEFAULT_VARIATION_COUNT; v++) {
            if (isDoor) {
              allTexturePaths.push(`/textures/Location/decorations/${decType}/${subtype}/${combo}/open/${v}.png`);
              allTexturePaths.push(`/textures/Location/decorations/${decType}/${subtype}/${combo}/closed/${v}.png`);
            }
            allTexturePaths.push(`/textures/Location/decorations/${decType}/${subtype}/${combo}/${v}.png`);
          }
        });
      }
    });

    // Стандартные пути с материалами
    materials.forEach(material => {
      combos.forEach(combo => {
        for (let v = 1; v <= DEFAULT_VARIATION_COUNT; v++) {
          if (isDoor) {
            allTexturePaths.push(`/textures/Location/decorations/${decType}/${material}/${combo}/open/${v}.png`);
            allTexturePaths.push(`/textures/Location/decorations/${decType}/${material}/${combo}/closed/${v}.png`);
          }
          allTexturePaths.push(`/textures/Location/decorations/${decType}/${material}/${combo}/${v}.png`);
        }
      });
    });
    // Комбо без материала
    combos.forEach(combo => {
      for (let v = 1; v <= DEFAULT_VARIATION_COUNT; v++) {
        if (isDoor) {
          allTexturePaths.push(`/textures/Location/decorations/${decType}/${combo}/open/${v}.png`);
          allTexturePaths.push(`/textures/Location/decorations/${decType}/${combo}/closed/${v}.png`);
        }
        allTexturePaths.push(`/textures/Location/decorations/${decType}/${combo}/${v}.png`);
      }
    });
    // Legacy flat files (в конце) — оставляем для совместимости
    sideNumbers.forEach(sideNumber => {
      if (isDoor) {
        allTexturePaths.push(`/textures/Location/decorations/${decType}/open/${sideNumber}.png`);
        allTexturePaths.push(`/textures/Location/decorations/${decType}/closed/${sideNumber}.png`);
      }
      allTexturePaths.push(`/textures/Location/decorations/${decType}/${sideNumber}.png`);
    });
  });

  const loadPromises = allTexturePaths.map(path => loadSideTexture(path));

  try {
    await Promise.all(loadPromises);
  } catch (error) {
    console.error('Ошибка при предзагрузке текстур decorationSides:', error);
  }
}