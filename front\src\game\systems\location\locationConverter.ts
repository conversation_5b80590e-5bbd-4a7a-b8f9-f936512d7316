/**
 * Конвертер между форматами Location и TransferLocation
 */

import { Location, TransferLocation, LocationMapCell, Point, LocationInteractive } from '../../../shared/types/Location';
import { MaterialTexture } from '../../../shared/enums';
import { LocationDecorations, LocationType, TerrainType } from '../../../shared/enums';
import { Position } from '../../../shared/types/Common';
import { DECORATION_LIGHT_CONSTANTS } from '../../utils/constants/timeLight';
import { isLightSource, getLightParameters, calculateLightLevel } from './visionSystem';

const NON_BLOCKING: Set<string> = new Set([
  LocationDecorations.PUDDLE,
  LocationDecorations.ROCKS,
  LocationDecorations.BUSH,
  LocationDecorations.TIRE,
  LocationDecorations.SIGN,
  LocationDecorations.GRASS,
  LocationDecorations.LITTER,
  LocationDecorations.LOG,
  LocationDecorations.MUD,
  LocationDecorations.FIRSTAID,
  LocationDecorations.BLACKBOARD,
  LocationDecorations.INTERIORLIGHT,
  LocationDecorations.CARPET,
  LocationDecorations.POSTER,
  LocationDecorations.STAIRS,
  LocationDecorations.SUBWAYTURNSTILE,
  LocationDecorations.NONE

]);

// Маппинг не нужен, возвращаем terrainType напрямую
function mapTerrainTypeToLocationCellLandType(terrainType: TerrainType): TerrainType {
  return terrainType;
}

/**
 * Создает статическую карту освещения от источников света
 * @param locationMap - карта локации
 * @param locationSize - размер локации
 * @returns массив точек освещения [x, y, lightLevel]
 */
function generateStaticLightMap(
  locationMap: Record<string, LocationMapCell>, 
  locationSize: { x: number; y: number }
): Array<[number, number, number]> {
  const lightMap: Array<[number, number, number]> = [];
  
  // Сначала находим все источники света
  const lightSources: Array<{ x: number; y: number; decoration: LocationDecorations }> = [];
  
  for (let y = 0; y < locationSize.y; y++) {
    for (let x = 0; x < locationSize.x; x++) {
      const key = `${x},${y}`;
      const cell = locationMap[key];
      
      if (cell?.decoration && isLightSource(cell.decoration)) {
        lightSources.push({ x, y, decoration: cell.decoration });
      }
    }
  }
  
  // Теперь для каждой точки на карте вычисляем освещение
  for (let y = 0; y < locationSize.y; y++) {
    for (let x = 0; x < locationSize.x; x++) {
      let maxLightLevel = 0;
      
      // Проверяем освещение от всех источников света
      for (const lightSource of lightSources) {
        const lightParams = getLightParameters(lightSource.decoration);
        if (lightParams) {
          const distance = Math.sqrt(
            Math.pow(x - lightSource.x, 2) + 
            Math.pow(y - lightSource.y, 2)
          );
          
          const lightLevel = calculateLightLevel(distance, lightParams.radius, lightParams.intensity);
          maxLightLevel = Math.max(maxLightLevel, lightLevel);
        }
      }
      
      // Если есть освещение, добавляем в карту
      if (maxLightLevel > DECORATION_LIGHT_CONSTANTS.MIN_LIGHT_LEVEL) {
        lightMap.push([x, y, maxLightLevel]);
      }
    }
  }
  
  return lightMap;
}

/**
 * Конвертирует TransferLocation в Location для работы в игре
 */
export function transferLocationToLocation(transferLocation: TransferLocation): Location {

  
  // Создаем locationMap из массивов координат
  const locationMap: Record<string, LocationMapCell> = {};

  // Для быстрого поиска decorationSides по координатам
  const decorationSidesMap = new Map<string, [number, number, number[]]>();
  if (Array.isArray(transferLocation.decorationSide)) {
    for (const ds of transferLocation.decorationSide) {
      const [x, y] = ds;
      decorationSidesMap.set(`${x},${y}`, ds);
    }
  }

  // Для быстрого поиска interactive (например, двери) по координатам — сохраняем сами объекты интерактива
  const interactiveMap = new Map<string, LocationInteractive>();
  if (Array.isArray(transferLocation.interactive)) {
    for (const it of transferLocation.interactive) {
      const pos = it.position;
      if (pos && typeof pos.x === 'number' && typeof pos.y === 'number') {
        interactiveMap.set(`${pos.x},${pos.y}`, it);
      }
    }
  }

  // Проходим по всем возможным координатам локации
  for (let y = 0; y < transferLocation.locationSize[1]; y++) {
    for (let x = 0; x < transferLocation.locationSize[0]; x++) {
      const key = `${x},${y}`;
      // Создаем базовую клетку
      const cell: LocationMapCell = {
        pos: { x, y },
        terrain: mapTerrainTypeToLocationCellLandType(transferLocation.terrain),
        terrainMarker: 0,
        blocked: false, // Временно устанавливаем false, будем обновлять ниже
        decoration: LocationDecorations.NONE,
        interactive: interactiveMap.get(key) ?? undefined,
        goBackZone: transferLocation.goBackPosition.some(([gx, gy]) => gx === x && gy === y),
        spawnZone: transferLocation.spawnPosition[0] === x && transferLocation.spawnPosition[1] === y
      };

      // Проверяем декорации из transferLocation
      if (transferLocation.decorations) {
        for (const [decorationKey, coordinates] of Object.entries(transferLocation.decorations)) {
          if (coordinates && Array.isArray(coordinates)) {
            const hasDecoration = coordinates.some(([dx, dy]) => dx === x && dy === y);
            if (hasDecoration) {
              cell.decoration = decorationKey as LocationDecorations;
              
              // Устанавливаем blocked в зависимости от типа декорации
              if (decorationKey === LocationDecorations.DOOR) {
                // Для дверей проверяем интерактивный объект
                const interactive = interactiveMap.get(key);
                if (interactive && interactive.type && typeof interactive.type === 'object' && 'isOpen' in interactive.type) {
                  cell.blocked = !(interactive.type as any).isOpen;
                } else {
                  // Нет интерактива — считаем дверь закрытой
                  cell.blocked = true;
                }
              } else if (!NON_BLOCKING.has(decorationKey)) {
                // Все остальные декорации блокируют, кроме тех что в NON_BLOCKING
                cell.blocked = true;
              }
              // Если декорация в NON_BLOCKING, blocked остается false
              break; // Прерываем цикл, так как нашли декорацию для этой клетки
            }
          }
        }
      }
      // Присваиваем decorationSides, если есть
      if (decorationSidesMap.has(key)) {
        cell.decorationSides = decorationSidesMap.get(key);
      }
      locationMap[key] = cell;
    }
  }

  const location: Location = {
    id: transferLocation.id,
    name: transferLocation.name,
    description: transferLocation.description,
    type: transferLocation.type,
    subtype: transferLocation.subtype,
    morality: transferLocation.morality,
    textureMaterial: transferLocation.textureMaterial ?? MaterialTexture.BETON,
    locationSize: {
      x: transferLocation.locationSize[0],
      y: transferLocation.locationSize[1]
    },
    locationMap,
    terrain: transferLocation.terrain,
    isDiscovered: transferLocation.isDiscovered,
    isVisible: transferLocation.isVisible,
    isExplored: false,
    playerPosition: {
      x: transferLocation.playerPosition[0],
      y: transferLocation.playerPosition[1]
    },
    playerPresent: transferLocation.playerPresent,
    faction: transferLocation.faction,
    createdAt: new Date(),
    lastUpdatedAt: new Date(),
    spawnPosition: {
      x: transferLocation.spawnPosition[0],
      y: transferLocation.spawnPosition[1]
    },
    goBackPosition: transferLocation.goBackPosition,
    staticLightMap: generateStaticLightMap(locationMap, { x: transferLocation.locationSize[0], y: transferLocation.locationSize[1] })
  };

  return location;
}

/**
 * Конвертирует Location обратно в TransferLocation для сохранения
 */
export function locationToTransferLocation(location: Location): TransferLocation {
  // Собираем декорации по типам динамически
  const decorations: Partial<Record<LocationDecorations, Point[]>> = {};
  const decorationSide: [number, number, number[]][] = [];
  const interactiveItems: LocationInteractive[] = [];
  for (const [key, cell] of Object.entries(location.locationMap)) {
    const [x, y] = key.split(',').map(Number);
    const dec = cell.decoration as LocationDecorations;
    if (dec && Object.values(LocationDecorations).includes(dec)) {
      if (!decorations[dec]) decorations[dec] = [];
      decorations[dec]!.push([x, y]);
    }
    if (cell.decorationSides && Array.isArray(cell.decorationSides) && cell.decorationSides.length === 3) {
      // [x, y, number[]]
      decorationSide.push([x, y, cell.decorationSides[2]]);
    }
    // Собираем интерактивные объекты (например, двери) в transfer-формат
    if (cell.interactive) {
      // Убедимся, что у интерактива есть корректная позиция
      const interactiveObj = { ...cell.interactive, position: cell.pos } as LocationInteractive;
      interactiveItems.push(interactiveObj);
    }
  }

  const transferLocation: TransferLocation = {
    id: location.id,
    name: location.name,
    description: location.description,
    locationSize: [location.locationSize.x, location.locationSize.y],
    type: location.type,
    subtype: location.subtype,
    morality: location.morality,
    textureMaterial: location.textureMaterial,
    terrain: location.terrain,
    playerPresent: location.playerPresent,
    playerPosition: [location.playerPosition.x, location.playerPosition.y],
    spawnPosition: [location.spawnPosition.x, location.spawnPosition.y],
    goBackPosition: location.goBackPosition,
    decorations,
    decorationSide: decorationSide.length > 0 ? decorationSide : undefined,
  interactive: interactiveItems.length > 0 ? interactiveItems : undefined,
    isDiscovered: location.isDiscovered,
    isVisible: location.isVisible,
    faction: location.faction
  };

  return transferLocation;
}