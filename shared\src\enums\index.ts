// === БАЗОВЫЕ ПОЛЬЗОВАТЕЛЬСКИЕ ЭНУМЫ === //
export enum UserRole {
  PLAYER = 'player',
  ADMIN = 'admin',
  MODERATOR = 'moderator'
}

export enum GameStatus {
  NOT_STARTED = 'not_started',
  IN_PROGRESS = 'in_progress',
  PAUSED = 'paused',
  COMPLETED = 'completed',
  GAME_OVER = 'game_over'
}

export enum Language {
  EN = 'en',
  PL = 'pl',
  UK = 'uk',
  RU = 'ru',
  DE = 'de',
  FR = 'fr',
  ES = 'es',
  CN = 'cn',
  JP = 'jp'
}
// === КВЕСТЫ === //
export enum QuestStatus {
  AVAILABLE = 'available',
  ACTIVE = 'active',
  COMPLETED = 'completed',
  FAILED = 'failed',
  LOCKED = 'locked'
}

export enum QuestType {
  MAIN = 'main',
  SIDE = 'side',
  RANDOM = 'random'
}

// === СОБЫТИЯ === //
export enum EventType {
  STORY = 'story',
  COMBAT = 'combat',
  CHOICE = 'choice',
  DISCOVERY = 'discovery'
}

// === ПРЕДМЕТЫ === //

export enum ItemType {
  CONSUMABLE = 'consumable',
  WEAPON = 'weapon',
  ARMOR = 'armor',
  TOOL = 'tool',
  RESOURCE = 'resource',
  QUEST_ITEM = 'quest_item'
}

export enum ItemRarity {
  COMMON = 'common',
  UNCOMMON = 'uncommon',
  RARE = 'rare',
  EPIC = 'epic',
  LEGENDARY = 'legendary'
}

// === ЛОКАЦИИ И МЕСТНОСТЬ === //
export enum LocationType {
  OUTDOOR = 'outdoor',        // Внешняя карта
  INDOOR = 'indoor',          // Внутри зданий  
  UNDERGROUND = 'underground',
  BEACH = 'beach'
}

export enum DecorationZoneType {
  SHOP = 'shop',
  BATHROOM = 'bathroom',

  FARM = 'farm',
  MILITARY = 'military',
  BUNKER = 'bunker',
  GASSTATION = 'gasstation',
  SCHOOL = 'school',
  HOSPITAL = 'hospital',
  HOTEL = 'hotel',
  BAR = 'bar',
  VILLAGE = 'village',
  SUBWAY = 'subway',
  POLICE = 'police',
  LABORATORY = 'laboratory',
  FACTORY = 'factory'

}
export enum LocationSubtype {
 
  TOWN = 'town',
  VILLAGE = 'village',
  CAMP = 'camp', 
  SHOP = 'shop',
  FARM = 'farm',
  MILITARY = 'military',
  BUNKER = 'bunker',
  GASSTATION = 'gasstation',
  SCHOOL = 'school',
  HOSPITAL = 'hospital',
  HOTEL = 'hotel',
  SUBWAY = 'subway',
  POLICE = 'police',
  LABORATORY = 'laboratory',
  FACTORY = 'factory',
  OTHER = 'other'

}
export enum RoofMaterial {
  TILES = 'tiles',
  WOOD = 'wood',
  METAL = 'metal',
}
export enum MaterialTexture {
  BRICK = 'brick',
  BETON = 'beton',
  WOOD = 'wood',
  METAL = 'metal',
}

export enum TerrainType {
  ASPHALT = 'asphalt',
  TILES = 'tiles',
  BETON = 'beton',
  WOOD = 'wood',
  GROUND = 'ground',
  METAL = 'metal',
  WATER = 'water',
  WASTELAND = 'wasteland'
}
export enum WorldMapDecorations {
  MOUNTAINS = 'mountains',
  FOREST = 'forest', 
  RIVER = 'river',
  ROAD = 'road',
  SWAMP = 'swamp',
  CITY = 'city',
  RUINS = 'ruins',
  LAKE = 'lake',
  RUBBLE = 'rubble',
  BRIDGE = 'bridge',
  BUSHES = 'bushes',
  VILLAGE = 'village',
  NONE = 'none' // Для пустых клеток
  
}

export enum LocationDecorations {
  ROCKS = 'rocks',
  TREE = 'tree',
  LOG = 'log',
  BUSH = 'bush',
  GRASS = 'grass',
  MOUNTAINWALL = 'mountainwall',
  WATER = 'water',
  PUDDLE = 'puddle',
  MUD = 'mud',

  FENCE = 'fence',
  WALL= 'wall',
  WINDOW = 'window',
  TENT = 'tent',

  FURNITURE = 'furniture',
  SOFA = 'sofa',
  CABINET = 'cabinet',
  TABLE = 'table',
  SHELF = 'shelf',
  CHAIR = 'chair',
  BED = 'bed',  
  INTERIORLIGHT = 'interiorLight',
  FRIDGE = 'fridge',
  OVEN = 'oven',
  TV = 'tv',
  TOILET = 'toilet',
  SINK = 'sink',
  BATH = 'bath',
  DOOR = 'door',

  MANHOLECOVER = 'manholeCover',
  TRASHBIN = 'trashBin',
  FIREPLACE = 'fireplace',

  SCELETON = 'sceleton',
  CORPSE = 'corpse',

  VENDINGMACHINE = 'vendingMachine',
  TERMINAL = 'terminal',
  SAFE =  'safe',
  GARAGEDOOR = 'garageDoor',
  WORKBENCH = 'workbench',
  BENCH = 'bench',
  WATERPUPM = 'waterPupm',
  TRASHCONTAINER = 'trashContainer',


// бар
  BARCOUNTER = 'barCounter',
  BARSHALF = 'barShelf',
  BARSTOOL = 'barStool',
  BILLIARDTABLE = 'billiardTable',
  POCKERTABLE = 'pockerTable',
  
// заправка
  GASSTATIONPUMP = 'gasStationPump',


 //производство
  PRODUCTIONMACHINE = 'productionMachine',
  PALLET = 'pallet',
  LOCKER  = 'locker',
  SEACONTAINER = 'seaContainer',
  RACK = 'rack',
  TOOLBOX = 'toolbox',
  LIQUIDTANK = 'liquidTank',
//  hotel
  CARPET = 'carpet',

  
  // магазин
  STORESHELF = 'storeShelf',
  CASHREGISTER = 'cashRegister',
  SHOPPINGCART = 'shoppingCart',


//school
  BLACKBOARD = 'blackboard',
  SCHOOLDESK = 'schoolDesk',
  SCHOOLCHAIR = 'schoolChair',
  BOOKSHELF = 'bookShelf',
  PARTITION = 'partition',


  // больница
  BIOMONITOR = 'bioMonitor',
  FIRSTAID = 'firstAid',
  SURGICALLAMP = 'surgicalTable',
  MEDCINECART = 'medcineCart',



  // военный
  MILITARYCONTAINER = 'militaryContainer',
  WEAPONRACK = 'weaponRack',
  BUNKBED = 'bunkBed',
  DEFENSESYSTEM = 'defenseSystem',
  WARMACHINE = 'warmachine',

  // ферма
  CORN = 'corn',
  POTATO = 'potato',
  CABBAGE = 'cabbage',
  ONION = 'onion',
  PEPPER = 'pepper',
  TOMATO = 'tomato',
  BEENS = 'beens',

  HIVE = 'hive',
  HAYBALE = 'haybale',
  // лоборатория 
  CHEMICALSTAND = 'chemicalStand',
  MASSIVECOMPUTER = 'massiveComputer',

  // полицейский участок
  WHITEBOARD = 'whiteboard',
  JAILBARS = 'jailBars',
  JAILDOOR = 'jailDoor',
  JAILWINDOW = 'jailWindow',


  // метро
  SUBWAYTURNSTILE = 'subwayTurnstile',
  POSTER = 'poster',

  STAIRS = 'stairs',
  VOID = 'void',
  ROAD = 'road',  
  BARREL = 'barrel',
  STREETLIGHT = 'streetlight',
  SIGN = 'sign',
  LITTER = 'litter',
  TIRE = 'tire',
  BOX = 'box',
  CAR = 'car',

  EXTERIORLIGHT = 'exteriorLight',
  
  UNIVERSALRND = 'universalrnd', // универсальный декор, может быть любым
  OTHER = 'other',
  NONE = 'none'
  }


// === NPC И ФРАКЦИИ === //

export enum NPCType {
  TRADER = 'trader',
  GUARD = 'guard',
  CIVILIAN = 'civilian',
  ENEMY = 'enemy',
  QUEST_GIVER = 'quest_giver'
}

export enum FactionAlignment {
  HOSTILE = 'hostile',
  NEUTRAL = 'neutral',
  FRIENDLY = 'friendly',
  ALLIED = 'allied'
}

// === КОНТЕЙНЕРЫ (УПРОЩЁННЫЕ) === //
export enum ContainerType {
  CHEST = 'chest',
  BARREL = 'barrel',
  CRATE = 'crate',
  SAFE = 'safe',
  CORPSE = 'corpse'
}

// === ЭФФЕКТЫ === //
export enum EffectType {
  HEALING = 'healing',
  POISON = 'poison',
  RADIATION = 'radiation',
  BUFF = 'buff',
  DEBUFF = 'debuff'
}

// === БОЙ === //
export enum BattlePhase {
  PREPARATION = 'preparation',
  PLAYER_TURN = 'player_turn',
  ENEMY_TURN = 'enemy_turn',
  RESULT = 'result',
  ENDED = 'ended'
}
