import { WorldMapCell } from "src/shared";
import { LocationConfig } from "../constants/locationConfig";
import { houseRoomSettings, HouseRoomSettings } from "../constants/houseRoomSettings";
import { LocationSubtype } from "../../../shared/enums";
import { Point } from "../../../shared/types/Location";
import { createBuilding,  adjustBuildingPosition, OccupiedArea } from "./buildingCreator";

// Интерфейс для маркера здания
interface BuildingMarker {
  position: Point;
  buildingConfig: HouseRoomSettings;
}

/**
 * Основная функция генерации случайных интерьеров для локаций
 *
 * Функция выполняет следующие задачи:
 * 1. Определяет центр карты и размеры локации
 * 2. Фильтрует подходящие типы зданий по canBeUsed
 * 3. Размещает маркеры зданий с отступами 3-5 тайлов от центра
 * 4. Создает здания без пересечений
 * 5. Для больших зданий (size: 3) размещает вложенные комнаты
 * 6. Устанавливает зоны декораций для правильного наполнения
 *
 * @param cell - Ячейка мира с локацией
 * @param config - Конфигурация локации
 * @param rng - Функция генерации случайных чисел
 */
export async function generateRundimInterior(
	cell: WorldMapCell,
	config: LocationConfig,
	rng: () => number,
): Promise<void> {

  //disable
  return;
  if (!cell.location) return;

  const location = cell.location;
  const [width, height] = location.locationSize;

  // Определяем центр карты
  const centerX = Math.floor(width / 2);
  const centerY = Math.floor(height / 2);

  // Фильтруем подходящие типы зданий для данной локации
  const availableBuildings = filterBuildingsForLocation(location.subtype);

  if (availableBuildings.length === 0) {
    console.warn(`Нет доступных зданий для локации типа: ${location.subtype}`);
    return;
  }

  // Определяем количество зданий для размещения
  const buildingCount = getBuildingCount(config, rng);

  // Получаем обязательные здания для данного типа локации
  const requiredBuildings = getRequiredBuildings(location.subtype, availableBuildings);

  // Размещаем маркеры зданий с отступами
  const buildingMarkers = placeBuildingMarkers(
    centerX,
    centerY,
    width,
    height,
    buildingCount,
    availableBuildings,
    requiredBuildings,
    rng
  );

  // Создаем здания по маркерам с проверкой коллизий и сдвигом
  const createdBuildings: Array<{ position: Point; size: number; config: HouseRoomSettings }> = [];
  const occupiedAreas: OccupiedArea[] = [];

  for (const marker of buildingMarkers) {
    // Определяем размеры здания (ширина и высота независимо)
  const buildingWidth = Math.floor(rng() * (marker.buildingConfig.xyMax - marker.buildingConfig.xyMin + 1)) + marker.buildingConfig.xyMin;
  const buildingHeight = Math.floor(rng() * (marker.buildingConfig.xyMax - marker.buildingConfig.xyMin + 1)) + marker.buildingConfig.xyMin;

    // Проверяем и корректируем позицию здания
    const adjustedPosition = adjustBuildingPosition(
      marker.position,
      buildingWidth,
      buildingHeight,
      occupiedAreas,
      width,
      height,
      2 // минимальный отступ между зданиями
    );

  // Создаем здание (только коробку без дверей и окон)
  await createBuilding(location, marker.buildingConfig, adjustedPosition, buildingWidth, buildingHeight, rng);

    // Добавляем в список созданных зданий
    createdBuildings.push({
      position: adjustedPosition,
      size: Math.max(buildingWidth, buildingHeight),
      config: marker.buildingConfig
    });

    // Добавляем в занятые области
    occupiedAreas.push({
      x: adjustedPosition[0],
      y: adjustedPosition[1],
      width: buildingWidth,
      height: buildingHeight
    });
  }
}

/**
 * Фильтрует здания, подходящие для данного типа локации
 */
function filterBuildingsForLocation(locationSubtype: LocationSubtype): HouseRoomSettings[] {
  return houseRoomSettings.filter(building => {
    // Проверяем, является ли здание отдельно стоящим
    if (!building.building) return false;

    // Проверяем, подходит ли для данного типа локации
    if (building.canBeUsed === 'all') return true;

    return Array.isArray(building.canBeUsed) && building.canBeUsed.includes(locationSubtype);
  });
}

/**
 * Получает обязательные здания для данного типа локации
 */
function getRequiredBuildings(locationSubtype: LocationSubtype, availableBuildings: HouseRoomSettings[]): HouseRoomSettings[] {
  return availableBuildings.filter(building => building.required);
}

/**
 * Определяет количество зданий для размещения на основе конфигурации
 */
function getBuildingCount(config: LocationConfig, rng: () => number): number {
  if (!config.buildings) return 0;

  const { min, max } = config.buildings;
  return Math.floor(rng() * (max - min + 1)) + min;
}

/**
 * Размещает маркеры зданий на карте с учетом отступов и коллизий
 */
function placeBuildingMarkers(
  centerX: number,
  centerY: number,
  width: number,
  height: number,
  buildingCount: number,
  availableBuildings: HouseRoomSettings[],
  requiredBuildings: HouseRoomSettings[],
  rng: () => number
): BuildingMarker[] {
  const markers: BuildingMarker[] = [];
  const occupiedAreas: { x: number; y: number; width: number; height: number }[] = [];

  const minDistanceFromCenter = 5;
  const maxDistanceFromCenter = Math.min(width, height) / 2.5;
  const minBuildingSpacing = 4; // Минимальное расстояние между зданиями
  const borderMargin = 3; // Отступ от краев карты

  // Сначала размещаем обязательные здания
  const requiredBuildingsToPlace = [...requiredBuildings];

  // Увеличиваем количество зданий, если есть обязательные
  const totalBuildingCount = Math.max(buildingCount, requiredBuildings.length);

  for (let attempt = 0; attempt < totalBuildingCount * 15 && markers.length < totalBuildingCount; attempt++) {
    let buildingConfig: HouseRoomSettings;

    // Если есть обязательные здания, размещаем их в первую очередь
    if (requiredBuildingsToPlace.length > 0) {
      const requiredIndex = Math.floor(rng() * requiredBuildingsToPlace.length);
      buildingConfig = requiredBuildingsToPlace.splice(requiredIndex, 1)[0];
    } else {
      // Выбираем случайное здание из доступных
      buildingConfig = availableBuildings[Math.floor(rng() * availableBuildings.length)];
    }

  // Определяем размеры здания (ширина и высота независимо)
  const buildingWidth = Math.floor(rng() * (buildingConfig.xyMax - buildingConfig.xyMin + 1)) + buildingConfig.xyMin;
  const buildingHeight = Math.floor(rng() * (buildingConfig.xyMax - buildingConfig.xyMin + 1)) + buildingConfig.xyMin;

    // Генерируем случайную позицию вокруг центра
    const angle = rng() * 2 * Math.PI;
    const distance = minDistanceFromCenter + rng() * (maxDistanceFromCenter - minDistanceFromCenter);

    const x = Math.round(centerX + Math.cos(angle) * distance);
    const y = Math.round(centerY + Math.sin(angle) * distance);

    // Проверяем, помещается ли здание в границы карты с учетом отступов
  if (x < borderMargin || y < borderMargin ||
    x + buildingWidth >= width - borderMargin ||
    y + buildingHeight >= height - borderMargin) {
      continue;
    }

    // Проверяем коллизии с другими зданиями (с учетом минимального расстояния)
    const newArea = {
      x: x - minBuildingSpacing,
      y: y - minBuildingSpacing,
      width: buildingWidth + minBuildingSpacing * 2,
      height: buildingHeight + minBuildingSpacing * 2
    };

    if (checkAreaCollision(newArea, occupiedAreas)) {
      continue;
    }

    // Дополнительная проверка: здание не должно быть слишком близко к центру
    const distanceToCenter = Math.sqrt((x - centerX) ** 2 + (y - centerY) ** 2);
    if (distanceToCenter < minDistanceFromCenter) {
      continue;
    }

    // Добавляем маркер
    markers.push({
      position: [x, y],
      buildingConfig
    });

    // Добавляем занятую область
    occupiedAreas.push({
      x,
      y,
      width: buildingWidth,
      height: buildingHeight
    });
  }

  return markers;
}

/**
 * Проверяет коллизию между областями
 */
function checkAreaCollision(
  newArea: { x: number; y: number; width: number; height: number },
  existingAreas: { x: number; y: number; width: number; height: number }[]
): boolean {
  for (const area of existingAreas) {
    if (
      newArea.x < area.x + area.width &&
      newArea.x + newArea.width > area.x &&
      newArea.y < area.y + area.height &&
      newArea.y + newArea.height > area.y
    ) {
      return true;
    }
  }
  return false;
}