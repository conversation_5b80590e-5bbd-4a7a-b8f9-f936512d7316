/**
 * Константы для ренд// Настройки тайлов
export const TILE_GAP = -0.18 // размер отступа между тайламиинга игры
 */

import { useGameStore } from '../../store/gameStore';
// Размеры экрана
export const IMCOMP_MAX_WIDTH = 1920
export const IMCOMP_MAX_HEIGHT = 1080

// Константы для изометрической проекции
export const BASE_TILE_WIDTH_LOCATION = 70
export const BASE_TILE_HEIGHT_LOCATION = 46

// Настройки рендеринга
export const TARGET_FPS = 60
export const FRAME_DURATION = 1000 / TARGET_FPS


// Настройки зума
export const MIN_ZOOM = 1
export const MAX_ZOOM = 1.4
export const ZOOM_STEP = 0.1

// Настройки тумана войны - простая прозрачность как в Fallout
export const FOG_TILE_OPACITY = 0.2 // Прозрачность клеток с туманом войны

// Настройки тайлов
export const TILE_GAP = -0.1 // размер отступа между тайлами

// Настройки UI обновления
export const CAMERA_UI_UPDATE_INTERVAL = 10 // мс

// Настройки размеров текстур игрока
export const PLAYER_TEXTURE_SIZE = useGameStore.getState().playerLocationPresent ? 150 : 400; // Размер текстур игрока в зависимости от наличия локации
// Настройки размеров текстур декораций
export const DECORATION_TEXTURE_SETTINGS = {
  // Базовые размеры (можно менять для экспериментов)
  DEFAULT_WIDTH: 64,
  DEFAULT_HEIGHT: 64,
  
  // Масштабирование для разных типов декораций
  SCALE_MULTIPLIERS: {
   
    // Декорации локаций
    ROCKS: 1.5, TREE: 4.5, LOG: 1.8, BUSH: 1.7, GRASS: 1.2,
    MOUNTAINWALL: 2.2, WATER: 1.0, PUDDLE: 1.1, MUDD: 1.0,
    FENCE: 1.0, FURNITURE: 1.0, WALL: 2.5, DOOR: 1.1,
    BARREL: 0.8, STREETLIGHT: 2.5, SIGN: 1.0, LITTER: 0.8,
    TIRE: 1.0, BOX: 1.3, CAR: 2.0, HAZARD: 1.0,
  },
  
  // Вертикальное смещение текстур (отрицательные значения - выше, положительные - ниже)
  VERTICAL_OFFSET: {
   
    // Декорации локаций
    ROCKS: -18, TREE: -117, LOG: -3, BUSH: -49, GRASS: -10,
    MOUNTAINWALL: -35, WATER: 0, PUDDLE: 2, MUDD: 0,
    FENCE: -8, FURNITURE: -10, WALL: -49, DOOR: -12,
    BARREL: -6, STREETLIGHT: -65, SIGN: -10, LITTER: 0,
    TIRE: -2, BOX: -25, CAR: -15, HAZARD: -8,
  },
  
  // Горизонтальное смещение текстур (отрицательные - влево, положительные - вправо)
  HORIZONTAL_OFFSET: {
  
    // Декорации локаций (все по центру)
    ROCKS: 0, TREE: 0, LOG: 0, BUSH: 0, GRASS: 0,
    MOUNTAINWALL: 0, WATER: 0, PUDDLE: 0, MUDD: 0,
    FENCE: 0, FURNITURE: 0, WALL: 0, DOOR: 0,
    BARREL: 0, STREETLIGHT: 0, SIGN: 0, LITTER: 0,
    TIRE: 0, BOX: 0, CAR: 0, HAZARD: 0,
  },
  
  // Настройки отрисовки
  ENABLE_SCALING: true,     // Включить/выключить масштабирование
  PRESERVE_ASPECT: true,    // Сохранять пропорции
  CENTER_ON_TILE: true,     // Центрировать на тайле
  ENABLE_OFFSET: true,      // Включить/выключить смещение
}

// Настройки размеров и поворота текстур местности для ЛОКАЦИЙ (TerrainType)
export const LOCATION_TERRAIN_TEXTURE_SETTINGS = {
  // Базовые размеры для текстур местности локаций
  DEFAULT_WIDTH: 80,
  DEFAULT_HEIGHT: 80,
  
  // Масштабирование ширины для разных типов местности в локациях
  WIDTH_SCALE: {
    ASPHALT: 1.0,      // Асфальт стандартно
    BETON: 1.15,        // Бетон чуть шире для лучших стыков
    WOOD: 1.0,         // Дерево стандартно
    METAL: 1.0,        // Металл стандартно  
    GROUND: 1.0,       // Земля стандартно
    WATER: 1.0,        // Вода стандартно
    WASTELAND: 1.2,    // Пустошь шире
  },
  
  // Масштабирование высоты для разных типов местности в локациях
  HEIGHT_SCALE: {
    ASPHALT: 1.0,      // Асфальт стандартно
    BETON:  0.88,        // Бетон чуть выше для лучших стыков
    WOOD: 1.0,         // Дерево стандартно
    METAL: 1.0,        // Металл стандартно
    GROUND: 1.0,       // Земля стандартно
    WATER: 1.5,        // Вода стандартно
    WASTELAND: 0.6,    // Пустошь ниже (изометрическая)
  },
  
  // Углы поворота для разных типов местности (в радианах)
  ROTATION_ANGLE: {
    ASPHALT: 0,        // Асфальт без поворота
    BETON: 0.005,      // Бетон поворот на 45 градусов (0.785 радиан)
    WOOD: 0,           // Дерево без поворота
    METAL: 0,          // Металл без поворота
    GROUND: 0,         // Земля без поворота
    WATER: 0,          // Вода без поворота
    WASTELAND: 0,      // Пустошь без поворота
  },
  
  // Вертикальное смещение текстур местности в локациях
  VERTICAL_OFFSET: {
    ASPHALT: 0,        // Асфальт по центру
    BETON: -1,         // Бетон чуть выше для лучших стыков
    WOOD: 0,           // Дерево по центру
    METAL: 0,          // Металл по центру
    GROUND: 0,         // Земля по центру
    WATER: 0,          // Вода по центру
    WASTELAND: 0,      // Пустошь по центру
  },
  
  // Горизонтальное смещение текстур местности в локациях
  HORIZONTAL_OFFSET: {
    ASPHALT: 0,        // Асфальт по центру
    BETON: -1,         // Бетон чуть левее для лучших стыков
    WOOD: 0,           // Дерево по центру
    METAL: 0,          // Металл по центру
    GROUND: 0,         // Земля по центру
    WATER: 0,          // Вода по центру
    WASTELAND: 0,      // Пустошь по центру
  },
  
  // Настройки отрисовки для локаций
  ENABLE_SCALING: true,      // Включить/выключить масштабирование
  ENABLE_ROTATION: true,     // Включить/выключить поворот
  ENABLE_OFFSET: true,       // Включить/выключить смещение
  CLIP_TO_DIAMOND: true,     // Обрезать по ромбу тайла
  SEPARATE_PROPORTIONS: true, // Использовать отдельные пропорции для ширины и высоты
  
  // Индивидуальные настройки для каждого типа текстуры (для отдельного натягивания)
  INDIVIDUAL_TEXTURE_SETTINGS: {
    BETON: {
      // Специальные настройки для бетона из-за проблем со стыками
      OVERLAP_CORRECTION: 2,    // Перекрытие в пикселях для устранения стыков
      EDGE_SMOOTHING: true,     // Сглаживание краев
      PATTERN_MATCH: true,      // Подгонка паттерна на стыках
    },
    WASTELAND: {
      // Настройки для пустоши (если они норм, оставляем как есть)
      TEXTURE_VARIATION: true,  // Использовать вариации текстуры
      BLEND_EDGES: false,       // Не размывать края (работает хорошо)
    },
    ASPHALT: {
      SEAMLESS_TILING: true,    // Бесшовное размножение
    },
    WOOD: {
      GRAIN_ALIGNMENT: true,    // Выравнивание текстуры дерева
    },
    METAL: {
      RUST_VARIATION: true,     // Вариации ржавчины
    },
    GROUND: {
      DIRT_BLENDING: true,      // Смешивание грунта
    },
    WATER: {
      FLOW_ANIMATION: false,    // Пока без анимации течения
    }
  }
}